import { Controller } from "@hotwired/stimulus";

// Badge Modal Controller
// Handles the display and interaction of detailed badge information in a modal
export default class extends Controller {
  static targets = [
    "container",
    "backdrop",
    "content",
    "badgeDisplay",
    "title",
    "description",
    "criteria",
    "closeButton",
  ];

  static values = {
    badgeId: Number,
    badgeName: String,
    badgeDescription: String,
    badgeIcon: String,
    badgeBackgroundColor: String,
    badgeTextColor: String,
    animationDuration: { type: Number, default: 300 },
  };

  connect() {
    console.log("badge-modal #connect");
    console.log("Badge modal controller connected");

    // Bind methods to maintain proper context
    this.boundHandleKeydown = this.handleKeydown.bind(this);
    this.boundHandleResize = this.handleResize.bind(this);
    this.boundHandleTouchStart = this.handleTouchStart.bind(this);
    this.boundHandleTouchEnd = this.handleTouchEnd.bind(this);

    // Check for reduced motion preference
    this.prefersReducedMotion = window.matchMedia(
      "(prefers-reduced-motion: reduce)"
    ).matches;

    // Detect touch device
    this.isTouchDevice =
      "ontouchstart" in window || navigator.maxTouchPoints > 0;

    // Initialize focus management
    this.previousActiveElement = null;

    // Set up resize listener for responsive behavior
    window.addEventListener("resize", this.boundHandleResize);

    // Set up touch listeners for mobile interactions
    if (this.isTouchDevice) {
      this.setupTouchListeners();
    }
  }

  disconnect() {
    // Clean up event listeners
    document.removeEventListener("keydown", this.boundHandleKeydown);
    window.removeEventListener("resize", this.boundHandleResize);

    // Restore body scroll if modal was open
    document.body.style.overflow = "";
  }

  // Set badge data for the modal (called by trigger controller)
  setBadgeData(badgeData) {
    console.log("setBadgeData called with:", badgeData);
    this.currentBadgeData = badgeData;
  }

  // Show modal (called by trigger controller)
  show() {
    console.log("show method called");
    if (this.currentBadgeData) {
      this.open(this.currentBadgeData);
    } else {
      console.error("No badge data set before showing modal");
    }
  }

  // Open modal with badge data
  open(badgeData) {
    console.log("Opening modal with badge data:", badgeData);

    // Store the currently focused element to restore later
    this.previousActiveElement = document.activeElement;

    // Populate modal with badge data
    this.populateModal(badgeData);

    // Show modal with animation
    this.showModal();

    // Set up keyboard navigation
    document.addEventListener("keydown", this.boundHandleKeydown);

    // Prevent body scroll
    document.body.style.overflow = "hidden";

    // Focus management
    this.manageFocus();

    // Trigger custom event for other controllers
    this.dispatch("opened", { detail: { badgeData } });
  }

  // Close modal
  close() {
    // Hide modal with animation
    this.hideModal();

    // Remove keyboard listener
    document.removeEventListener("keydown", this.boundHandleKeydown);

    // Restore body scroll
    document.body.style.overflow = "";

    // Restore focus to previous element
    if (this.previousActiveElement) {
      this.previousActiveElement.focus();
      this.previousActiveElement = null;
    }

    // Trigger custom event
    this.dispatch("closed");
  }

  // Populate modal content with badge data
  populateModal(badgeData) {
    const { name, description, icon, backgroundColor, textColor, criteria } =
      badgeData;

    // Update title
    if (this.hasTitleTarget) {
      this.titleTarget.textContent = name;
    }

    // Update description
    if (this.hasDescriptionTarget) {
      this.descriptionTarget.textContent = description;
    }

    // Update criteria if provided
    if (this.hasCriteriaTarget && criteria) {
      this.criteriaTarget.querySelector("p").textContent = criteria;
    }

    // Create and insert the badge display
    this.createBadgeDisplay(badgeData);
  }

  // Create the large badge display for the modal
  createBadgeDisplay(badgeData) {
    const { name, icon, backgroundColor, textColor } = badgeData;

    if (!this.hasBadgeDisplayTarget) return;

    // Create badge element with holographic effects
    const badgeElement = document.createElement("div");
    badgeElement.className = `
      badge badge-lg inline-flex items-center font-medium rounded-xl border shadow-lg
      backdrop-filter backdrop-blur-sm bg-opacity-90 transition-all duration-200 ease-out
      transform-gpu will-change-transform cursor-default px-6 py-3 text-lg
      prismatic depth-effect overflow-hidden
    `
      .trim()
      .replace(/\s+/g, " ");

    // Apply badge colors with proper border-radius handling
    badgeElement.style.cssText = `
      background-color: ${backgroundColor} !important;
      color: ${textColor} !important;
      border-color: ${backgroundColor} !important;
      border-radius: 0.75rem !important;
    `;

    // Create icon element if icon exists
    let iconHTML = "";
    if (icon) {
      iconHTML = `<i class="badge-icon text-lg mr-3 transition-transform duration-200 ease-out" aria-hidden="true" data-phosphor="${icon}"></i>`;
    }

    // Create badge content
    badgeElement.innerHTML = `
      ${iconHTML}
      <span class="badge-name font-medium">${name}</span>
    `;

    // Clear previous content and insert new badge
    this.badgeDisplayTarget.innerHTML = "";
    this.badgeDisplayTarget.appendChild(badgeElement);

    // Initialize Phosphor icons if they exist
    this.initializePhosphorIcons(badgeElement);
  }

  // Initialize Phosphor icons in the badge
  initializePhosphorIcons(container) {
    const iconElements = container.querySelectorAll("[data-phosphor]");
    iconElements.forEach((element) => {
      const iconName = element.dataset.phosphor;
      if (iconName && window.phosphor) {
        // Replace with actual Phosphor icon if library is available
        element.innerHTML = window.phosphor[iconName] || "";
      }
    });
  }

  // Show modal with animation
  showModal() {
    // Remove hidden class
    this.containerTarget.classList.remove("hidden");

    // Force reflow for animation
    this.containerTarget.offsetHeight;

    // Add entrance animation class
    if (!this.prefersReducedMotion) {
      this.contentTarget.classList.add("modal-enter");
    }

    // Use view transitions if supported
    if (document.startViewTransition) {
      document.startViewTransition(() => {
        this.containerTarget.style.opacity = "1";
      });
    }
  }

  // Hide modal with animation
  hideModal() {
    const duration = this.prefersReducedMotion
      ? 0
      : this.animationDurationValue;

    // Add exit animation
    if (!this.prefersReducedMotion) {
      this.contentTarget.style.transform = "scale(0.95) translateY(20px)";
      this.contentTarget.style.opacity = "0";
      this.containerTarget.style.opacity = "0";
    }

    // Hide after animation completes
    setTimeout(() => {
      this.containerTarget.classList.add("hidden");

      // Reset styles for next opening
      this.contentTarget.style.transform = "";
      this.contentTarget.style.opacity = "";
      this.containerTarget.style.opacity = "";
      this.contentTarget.classList.remove("modal-enter");
    }, duration);
  }

  // Handle keyboard navigation
  handleKeydown(event) {
    switch (event.key) {
      case "Escape":
        event.preventDefault();
        this.close();
        break;
      case "Tab":
        this.handleTabNavigation(event);
        break;
    }
  }

  // Handle tab navigation within modal
  handleTabNavigation(event) {
    const focusableElements = this.containerTarget.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (event.shiftKey) {
      // Shift + Tab
      if (document.activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
    } else {
      // Tab
      if (document.activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  }

  // Manage focus when modal opens
  manageFocus() {
    // Focus the close button by default for accessibility
    if (this.hasCloseButtonTarget) {
      this.closeButtonTarget.focus();
    } else {
      // Fallback to finding close button
      const closeButton = this.containerTarget.querySelector(
        'button[aria-label*="Close"]'
      );
      if (closeButton) {
        closeButton.focus();
      }
    }

    // Announce modal opening to screen readers
    this.announceToScreenReader("Badge details modal opened");
  }

  // Announce information to screen readers
  announceToScreenReader(message) {
    const announcement = document.createElement("div");
    announcement.setAttribute("aria-live", "polite");
    announcement.setAttribute("aria-atomic", "true");
    announcement.className = "sr-only";
    announcement.textContent = message;

    document.body.appendChild(announcement);

    // Remove announcement after screen reader has time to read it
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }

  // Handle window resize for responsive behavior
  handleResize() {
    // Adjust modal positioning if needed for responsive behavior
    if (
      this.hasContainerTarget &&
      !this.containerTarget.classList.contains("hidden")
    ) {
      // Recalculate modal positioning on resize
      this.adjustModalForViewport();
    }
  }

  // Set up touch listeners for mobile interactions
  setupTouchListeners() {
    if (this.hasContainerTarget) {
      this.containerTarget.addEventListener(
        "touchstart",
        this.boundHandleTouchStart,
        { passive: true }
      );
      this.containerTarget.addEventListener(
        "touchend",
        this.boundHandleTouchEnd,
        { passive: true }
      );
    }
  }

  // Handle touch start for mobile interactions
  handleTouchStart(event) {
    this.touchStartY = event.touches[0].clientY;
    this.touchStartTime = Date.now();
  }

  // Handle touch end for mobile interactions (swipe to close)
  handleTouchEnd(event) {
    if (!this.touchStartY) return;

    const touchEndY = event.changedTouches[0].clientY;
    const touchDuration = Date.now() - this.touchStartTime;
    const swipeDistance = touchEndY - this.touchStartY;

    // Close modal on downward swipe (mobile pattern)
    if (swipeDistance > 100 && touchDuration < 300) {
      this.close();
    }

    // Reset touch tracking
    this.touchStartY = null;
    this.touchStartTime = null;
  }

  // Adjust modal positioning for current viewport
  adjustModalForViewport() {
    const isMobile = window.innerWidth < 640;

    if (isMobile && this.hasContentTarget) {
      // Ensure modal doesn't exceed viewport height on mobile
      const maxHeight = window.innerHeight * 0.9;
      this.contentTarget.style.maxHeight = `${maxHeight}px`;
    }
  }

  // Prevent event propagation when clicking on modal content
  stopPropagation(event) {
    event.stopPropagation();
  }

  // Action methods that can be called from templates

  // Open modal action (can be triggered from badge clicks)
  openModal(event) {
    event.preventDefault();

    // Extract badge data from the clicked element or its data attributes
    const badgeElement = event.currentTarget;
    const badgeData = this.extractBadgeData(badgeElement);

    this.open(badgeData);
  }

  // Extract badge data from DOM element
  extractBadgeData(element) {
    // Look for data attributes or traverse to find badge information
    const badgeContainer = element.closest("[data-badge-id]") || element;

    return {
      id: badgeContainer.dataset.badgeId,
      name: badgeContainer.dataset.badgeName || "Badge",
      description:
        badgeContainer.dataset.badgeDescription || "Badge description",
      icon: badgeContainer.dataset.badgeIcon || "",
      backgroundColor: badgeContainer.dataset.badgeBackgroundColor || "#3B82F6",
      textColor: badgeContainer.dataset.badgeTextColor || "#FFFFFF",
      criteria:
        badgeContainer.dataset.badgeCriteria ||
        "This badge represents exceptional achievement and is awarded to recognize outstanding contributions to the platform.",
    };
  }
}
