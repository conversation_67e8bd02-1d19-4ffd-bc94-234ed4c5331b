<%#
  Badge Component - Full Size Version

  Usage:
  render 'shared/badge', badge_type: badge_type
  render 'shared/badge', badge_type: badge_type, size: 'lg'
  render 'shared/badge', badge_type: badge_type, show_description: true

  Parameters:
  - badge_type: BadgeType object (required)
  - size: 'sm', 'md' (default), 'lg'
  - show_description: boolean (default: false) - shows description on hover
  - class_name: additional CSS classes
  - data_attributes: hash of additional data attributes
%>

<%
  # Set default values
  badge_type = local_assigns.fetch(:badge_type)
  size = local_assigns.fetch(:size, 'md')
  show_description = local_assigns.fetch(:show_description, false)
  class_name = local_assigns.fetch(:class_name, '')
  data_attributes = local_assigns.fetch(:data_attributes, {})
  
  # Validate badge_type
  unless badge_type.is_a?(BadgeType)
    raise ArgumentError, "badge_type must be a BadgeType object"
  end
  
  # Size-based styling
  size_classes = case size
  when 'sm'
    'px-2 py-1 text-xs'
  when 'lg'
    'px-4 py-2.5 text-base'
  else # 'md' default
    'px-3 py-1.5 text-sm'
  end
  
  # Icon size based on badge size
  icon_size_classes = case size
  when 'sm'
    'text-xs mr-1'
  when 'lg'
    'text-base mr-2'
  else # 'md' default
    'text-sm mr-1.5'
  end
  
  # Build CSS styles from badge_type
  badge_styles = [
    "background-color: #{badge_type.background_color}",
    "color: #{badge_type.text_color}",
    "border-color: #{badge_type.background_color}"
  ].join('; ')
  
  # Combine all CSS classes with holographic effects
  badge_classes = [
    'badge',
    'inline-flex',
    'items-center',
    'font-medium',
    'rounded-lg',
    'border',
    'shadow-sm',
    'backdrop-filter',
    'backdrop-blur-sm',
    'bg-opacity-90',
    'transition-all',
    'duration-200',
    'ease-out',
    'transform-gpu',
    'will-change-transform',
    'cursor-default',
    'prismatic',
    'depth-effect',
    size_classes,
    class_name
  ].compact.join(' ')
  
  # Prepare data attributes for Stimulus controllers with holographic values and click functionality
  final_data_attributes = {
    controller: 'badge badge-click',
    badge_target: 'badge',
    badge_click_target: 'badge',
    badge_holographic_intensity_value: 0.4,
    badge_rotation_factor_value: 12,
    badge_glow_intensity_value: 0.8,
    badge_prismatic_effect_value: true,
    badge_scanline_effect_value: false,
    badge_refraction_pattern_value: 'none',
    badge_depth_effect_value: true,
    badge_glitch_effect_value: false,
    badge_shadow_color_value: 'rgba(0, 0, 0, 0.5)',
    badge_click_badge_id_value: badge_type.id,
    badge_click_badge_name_value: badge_type.name,
    badge_click_badge_description_value: badge_type.description,
    badge_click_badge_icon_value: badge_type.icon,
    badge_click_badge_background_color_value: badge_type.background_color,
    badge_click_badge_text_color_value: badge_type.text_color,
    badge_click_badge_criteria_value: "This badge is awarded for #{badge_type.name.downcase} and represents exceptional achievement on the platform."
  }.merge(data_attributes)
  
  # Tooltip content for description
  tooltip_content = show_description ? badge_type.description : nil
%>

<div
  class="<%= badge_classes %>"
  style="<%= badge_styles %>"
  <% final_data_attributes.each do |key, value| %>
    data-<%= key.to_s.dasherize %>="<%= value %>"
  <% end %>
  role="img"
  aria-label="<%= html_escape("#{badge_type.name} badge") %>"
>
  <%# Badge Icon %>
  <% if badge_type.icon.present? %>
    <%= phosphor_icon badge_type.icon, class: "badge-icon #{icon_size_classes}", "aria-hidden": "true" %>
  <% end %>

  <%# Badge Name %>
  <span class="badge-name font-medium">
    <%= badge_type.name %>
  </span>
</div>

<%# Enhanced CSS for badge effects %>
<style>
  .badge {
    position: relative;
    overflow: hidden;
  }
  
  .badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
    border-radius: inherit;
  }
  

  
  /* Ensure smooth transitions for all badge elements */
  .badge-icon {
    transition: transform 0.2s ease-out;
  }
  
  /* Accessibility: Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .badge {
      transition: none !important;
    }
    
    .badge-icon {
      transition: none !important;
    }
    
    .badge:hover {
      transform: none !important;
    }
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .badge {
      border-width: 2px;
      backdrop-filter: none;
      background-opacity: 1 !important;
    }
  }
  
  /* Print styles */
  @media print {
    .badge {
      background: white !important;
      color: black !important;
      border: 1px solid black !important;
      box-shadow: none !important;
      transform: none !important;
    }
    

  }
</style>
